/**
 * PERFORMANCE OPTIMIZATION UTILITY CLASS
 * Created for Click_NewChildCase performance improvement
 * Provides caching and bulk processing capabilities
 */
public with sharing class OnCallPerformanceUtility {
    
    // Cache maps for frequently accessed data
    private static Map<Id, Engagement__c> engagementCache = new Map<Id, Engagement__c>();
    private static Map<Id, Account> accountCache = new Map<Id, Account>();
    private static Map<String, String> timezoneCache = new Map<String, String>();
    private static Map<String, String> hubCache = new Map<String, String>();
    private static Map<String, Id> recordTypeCache = new Map<String, Id>();
    
    // Cache expiry tracking
    private static DateTime cacheExpiry = DateTime.now().addHours(1);
    
    static {
        initializeRecordTypeCache();
    }
    
    /**
     * Initialize record type cache to avoid repeated schema calls
     */
    private static void initializeRecordTypeCache() {
        Map<String, Schema.RecordTypeInfo> recordTypes = Schema.SObjectType.Case.getRecordTypeInfosByName();
        for(String rtName : recordTypes.keySet()) {
            recordTypeCache.put(rtName, recordTypes.get(rtName).getRecordTypeId());
        }
    }
    
    /**
     * Get record type ID from cache
     */
    public static Id getRecordTypeId(String recordTypeName) {
        return recordTypeCache.get(recordTypeName);
    }
    
    /**
     * Bulk fetch engagements with caching
     */
    public static Map<Id, Engagement__c> getEngagements(Set<Id> engagementIds) {
        Map<Id, Engagement__c> result = new Map<Id, Engagement__c>();
        Set<Id> idsToQuery = new Set<Id>();
        
        // Check cache first
        for(Id engId : engagementIds) {
            if(engagementCache.containsKey(engId) && !isCacheExpired()) {
                result.put(engId, engagementCache.get(engId));
            } else {
                idsToQuery.add(engId);
            }
        }
        
        // Query missing engagements
        if(!idsToQuery.isEmpty()) {
            List<Engagement__c> engagements = [
                SELECT Id, Program_Status_Eng__c, Market__c, Client_Clinical_Program_ID__r.Clinical_Program__c,
                       Consumer__c, Program_End_Date__c
                FROM Engagement__c 
                WHERE Id IN :idsToQuery
            ];
            
            for(Engagement__c eng : engagements) {
                result.put(eng.Id, eng);
                engagementCache.put(eng.Id, eng); // Update cache
            }
        }
        
        return result;
    }
    
    /**
     * Bulk fetch accounts with caching
     */
    public static Map<Id, Account> getAccounts(Set<Id> accountIds) {
        Map<Id, Account> result = new Map<Id, Account>();
        Set<Id> idsToQuery = new Set<Id>();
        
        // Check cache first
        for(Id accId : accountIds) {
            if(accountCache.containsKey(accId) && !isCacheExpired()) {
                result.put(accId, accountCache.get(accId));
            } else {
                idsToQuery.add(accId);
            }
        }
        
        // Query missing accounts
        if(!idsToQuery.isEmpty()) {
            List<Account> accounts = [
                SELECT Id, Market__c, Name
                FROM Account 
                WHERE Id IN :idsToQuery
            ];
            
            for(Account acc : accounts) {
                result.put(acc.Id, acc);
                accountCache.put(acc.Id, acc); // Update cache
            }
        }
        
        return result;
    }
    
    /**
     * Get timezone for market with caching
     */
    public static String getTimezoneForMarket(String marketName) {
        if(String.isBlank(marketName)) return null;
        
        // Check cache first
        if(timezoneCache.containsKey(marketName) && !isCacheExpired()) {
            return timezoneCache.get(marketName);
        }
        
        try {
            String engMarket = marketName.replace(' ','_').replace('-','_'); 
            On_Call_Market_Vs_Timezone__mdt timezoneMdt = On_Call_Market_Vs_Timezone__mdt.getInstance(engMarket);
            
            if(timezoneMdt != null && String.isNotBlank(timezoneMdt.Time_Zone__c)) {
                String formattedTime = Datetime.now().format('M/d/yyyy h:mm a z', timezoneMdt.Time_Zone__c);
                timezoneCache.put(marketName, formattedTime); // Cache result
                return formattedTime;
            }
        } catch(Exception e) {
            System.debug('Error calculating timezone for market ' + marketName + ': ' + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Get hub for market with caching
     */
    public static String getHubForMarket(String marketName) {
        if(String.isBlank(marketName)) return null;
        
        // Check cache first
        if(hubCache.containsKey(marketName) && !isCacheExpired()) {
            return hubCache.get(marketName);
        }
        
        try {
            String marketApi = marketName.contains('-') ? 
                              marketName.replaceAll('-', '_') : 
                              marketName.replaceAll(' ', '_');
            
            On_Call_Market_Vs_Hub__mdt hubMdt = On_Call_Market_Vs_Hub__mdt.getInstance(marketApi);
            String hub = (hubMdt != null) ? hubMdt.Hub__c : '';
            
            hubCache.put(marketName, hub); // Cache result
            return hub;
        } catch(Exception e) {
            System.debug('Error getting hub for market ' + marketName + ': ' + e.getMessage());
            return '';
        }
    }
    
    /**
     * Bulk create child cases with optimized performance
     */
    public static List<Case> createChildCasesBulk(List<Case> parentCases, Boolean isNonMember) {
        List<Case> childCases = new List<Case>();
        
        // Collect all required IDs for bulk queries
        Set<Id> engagementIds = new Set<Id>();
        Set<Id> facilityIds = new Set<Id>();
        
        for(Case parentCase : parentCases) {
            if(parentCase.Engagement__c != null) {
                engagementIds.add(parentCase.Engagement__c);
            }
            if(isNonMember && parentCase.Facility__c != null) {
                facilityIds.add(parentCase.Facility__c);
            }
        }
        
        // Bulk query all required data
        Map<Id, Engagement__c> engagementMap = getEngagements(engagementIds);
        Map<Id, Account> accountMap = getAccounts(facilityIds);
        
        // Create child cases with optimized data access
        for(Case parentCase : parentCases) {
            Case childCase = createOptimizedChildCase(parentCase, engagementMap, accountMap, isNonMember);
            if(childCase != null) {
                childCases.add(childCase);
            }
        }
        
        // Bulk insert
        if(!childCases.isEmpty()) {
            try {
                AccessSecurity.insertAsUser(childCases, null);
            } catch(Exception e) {
                System.debug('Error in bulk child case creation: ' + e.getMessage());
                throw e;
            }
        }
        
        return childCases;
    }
    
    /**
     * Create optimized child case
     */
    private static Case createOptimizedChildCase(Case parentCase, Map<Id, Engagement__c> engagementMap, 
                                               Map<Id, Account> accountMap, Boolean isNonMember) {
        try {
            Case childCase = parentCase.clone(false, true, false, false);
            childCase.ParentId = parentCase.Id;
            childCase.OwnerId = UserInfo.getUserId();
            childCase.Subject = 'On Call Support';
            childCase.Status = 'Entered In Error';
            childCase.Admission__c = 'Other';
            childCase.Person_Calling__c = '';
            childCase.Inbound_or_Outbound__c = 'Outbound';
            childCase.description = '';
            childCase.Date_and_Time__c = System.now();
            
            // Use bulk-queried data
            if(childCase.Engagement__c != null && engagementMap.containsKey(childCase.Engagement__c)) {
                Engagement__c eng = engagementMap.get(childCase.Engagement__c);
                childCase.Market__c = eng.Market__c;
                childCase.Clinical_Program__c = eng.Client_Clinical_Program_ID__r.Clinical_Program__c;
                childCase.Members_Time_Zone__c = getTimezoneForMarket(eng.Market__c);
            }
            
            if(isNonMember && childCase.Facility__c != null && accountMap.containsKey(childCase.Facility__c)) {
                childCase.Market__c = accountMap.get(childCase.Facility__c).Market__c;
            }
            
            return childCase;
            
        } catch(Exception e) {
            System.debug('Error creating optimized child case: ' + e.getMessage());
            return null;
        }
    }
    
    /**
     * Check if cache has expired
     */
    private static Boolean isCacheExpired() {
        return DateTime.now() > cacheExpiry;
    }
    
    /**
     * Clear all caches (useful for testing or memory management)
     */
    public static void clearAllCaches() {
        engagementCache.clear();
        accountCache.clear();
        timezoneCache.clear();
        hubCache.clear();
        cacheExpiry = DateTime.now().addHours(1);
    }
    
    /**
     * Get cache statistics for monitoring
     */
    public static Map<String, Integer> getCacheStatistics() {
        Map<String, Integer> stats = new Map<String, Integer>();
        stats.put('engagementCacheSize', engagementCache.size());
        stats.put('accountCacheSize', accountCache.size());
        stats.put('timezoneCacheSize', timezoneCache.size());
        stats.put('hubCacheSize', hubCache.size());
        stats.put('recordTypeCacheSize', recordTypeCache.size());
        return stats;
    }
    
    /**
     * Warm up caches with frequently accessed data
     */
    public static void warmUpCaches(Set<Id> engagementIds, Set<Id> accountIds, Set<String> markets) {
        if(!engagementIds.isEmpty()) {
            getEngagements(engagementIds);
        }
        
        if(!accountIds.isEmpty()) {
            getAccounts(accountIds);
        }
        
        if(!markets.isEmpty()) {
            for(String market : markets) {
                getTimezoneForMarket(market);
                getHubForMarket(market);
            }
        }
    }
}
