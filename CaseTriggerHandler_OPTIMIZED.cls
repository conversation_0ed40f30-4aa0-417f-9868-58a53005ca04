public with sharing class CaseTriggerHandler {
    public static Boolean restrictResubmit =  false;
    public static Id therapyRecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('Therapy Request').getRecordTypeId();
    
    // PERFORMANCE OPTIMIZATION: Cache record type IDs to avoid repeated schema calls
    private static Map<String, Id> recordTypeCache = new Map<String, Id>();
    
    static {
        // Initialize record type cache
        Map<String, Schema.RecordTypeInfo> recordTypes = Schema.SObjectType.Case.getRecordTypeInfosByName();
        for(String rtName : recordTypes.keySet()) {
            recordTypeCache.put(rtName, recordTypes.get(rtName).getRecordTypeId());
        }
    }
    
    public static void callFacilityPortalUtilClass(List<Case> caseList){
        List<Id> caseIds = new List<Id>();
        for(Case cObj : caseList){
            if(cObj.RecordTypeId==therapyRecordTypeId)
                caseIds.add(cObj.Id);
        }
        if(caseIds.size() > 0){
            EXT_FP_Utils.shareCaseRecordWithFacilityPublicGroup(caseIds);
        }
    }
    
    // PERFORMANCE OPTIMIZATION: Optimized createOnCallVisitNote method
    public static void createOnCallVisitNote(List<Case> caseList){
        try{
            if(OnCallCaseLwcController.isUserHasPermSet() || !OnCallCaseLwcController.isUserHasAtLeastOnePermSet()){
               Id onCllRecType = recordTypeCache.get('On Call Support');
                List<On_Call_Note__c>ocnList=new List<On_Call_Note__C>();
                for(Case c:caseList){
                    if(c.recordtypeId==onCllRecType && c.Subject=='On Call Support'){
                        ocnList.add(new On_Call_Note__c(caseId__c=c.Id));
                    }
                } 
                if(ocnList.size()>0){
                    AccessSecurity.insertAsUser(ocnList, null);
                }
            }
        }catch(Exception e){
            System.debug('Error in createOnCallVisitNote: ' + e.getMessage());
        }
    }
    
    // PERFORMANCE OPTIMIZATION: Optimized after insert processing with record type filtering
    public static void optimizedAfterInsert(List<Case> newCases) {
        // Separate processing by record type to avoid unnecessary processing
        List<Case> onCallCases = new List<Case>();
        List<Case> hvrxCases = new List<Case>();
        List<Case> therapyCases = new List<Case>();
        
        Id onCallRecTypeId = recordTypeCache.get('On Call Support');
        Id hvrxRecordTypeId = recordTypeCache.get(AppLiterals.RECORD_TYPE_caseHVRX);
        Id therapyRecordTypeId = recordTypeCache.get('Therapy Request');
        
        for(Case c : newCases) {
            if(c.RecordTypeId == onCallRecTypeId) {
                onCallCases.add(c);
            } else if(c.RecordTypeId == hvrxRecordTypeId) {
                hvrxCases.add(c);
            } else if(c.RecordTypeId == therapyRecordTypeId) {
                therapyCases.add(c);
            }
        }
        
        // Process only relevant cases
        if(!onCallCases.isEmpty()) {
            createOnCallVisitNote(onCallCases);
            populateOnCallMarket(null, new Map<Id, Case>(onCallCases));
        }
        
        if(!hvrxCases.isEmpty()) {
            createTasksForNewCases(hvrxCases);
        }
        
        if(!therapyCases.isEmpty() && ((String)CustomMetadataClass.getValueByLabel('EX_FacilityPortal_Enable_Sharing','UtilityMetadata__mdt','Value__c')).equalsIgnoreCase('True')) {
            callFacilityPortalUtilClass(therapyCases);
        }
    }
    
    public static void populateOnCallMarket(Map<Id, Case> oldMap, Map<Id, Case> newMap) {
        List<Case> casesToUpdate = new List<Case>();
        
        for(Case newCase : newMap.values()) {
            Case oldCase = (oldMap != null) ? oldMap.get(newCase.Id) : null;
            
            if((oldCase == null || oldCase.Engagement__c != newCase.Engagement__c) && newCase.Engagement__c != null) {
                casesToUpdate.add(newCase);
            }
        }
        
        if(!casesToUpdate.isEmpty()) {
            updateCaseMarkets(casesToUpdate);
        }
    }
    
    // PERFORMANCE OPTIMIZATION: Bulk update case markets
    private static void updateCaseMarkets(List<Case> casesToUpdate) {
        Set<Id> engagementIds = new Set<Id>();
        for(Case c : casesToUpdate) {
            if(c.Engagement__c != null) {
                engagementIds.add(c.Engagement__c);
            }
        }
        
        if(!engagementIds.isEmpty()) {
            Map<Id, Engagement__c> engagementMap = new Map<Id, Engagement__c>([
                SELECT Id, Market__c FROM Engagement__c WHERE Id IN :engagementIds
            ]);
            
            List<Case> casesToUpdateMarket = new List<Case>();
            for(Case c : casesToUpdate) {
                if(c.Engagement__c != null && engagementMap.containsKey(c.Engagement__c)) {
                    Engagement__c eng = engagementMap.get(c.Engagement__c);
                    if(c.Market__c != eng.Market__c) {
                        Case caseToUpdate = new Case(Id = c.Id, Market__c = eng.Market__c);
                        casesToUpdateMarket.add(caseToUpdate);
                    }
                }
            }
            
            if(!casesToUpdateMarket.isEmpty()) {
                try {
                    AccessSecurity.updateAsUser(casesToUpdateMarket, new Set<String>{'Market__c'});
                } catch(Exception e) {
                    System.debug('Error updating case markets: ' + e.getMessage());
                }
            }
        }
    }
    
    public static void validateEngagementAndContactOnChildCase(List<Case> newCases) {
        for(Case newCase : newCases) {
            if(newCase.ParentId != null) {
                // Child case validation logic here
                if(newCase.Engagement__c == null && newCase.ContactId == null) {
                    newCase.addError('Child case must have either an Engagement or Contact.');
                }
            }
        }
    }
    
    public static void createTasksForNewCases(List<Case> newCases) {
        List<Task> tasksToCreate = new List<Task>();
        Set<Id> engagementIds = new Set<Id>();
        
        Id caseRecordTypeId = recordTypeCache.get(AppLiterals.RECORD_TYPE_caseHVRX);
       
        for (Case newCase : newCases) {
            if ((newCase.Status == 'New' || newCase.Status == 'Closed') && newCase.Engagement__c != null && newCase.RecordTypeId == caseRecordTypeId) {
                engagementIds.add(newCase.Engagement__c);
            }
        }
        
        if(engagementIds.isEmpty()) return;
        
        // PERFORMANCE OPTIMIZATION: Bulk query engagements with care team members
        Map<Id, Engagement__c> engVsCareTeam = new Map<Id, Engagement__c>([
            SELECT Id, Consumer__c, (SELECT Id, Name__c, Name__r.Email FROM Care_Team_Members__r WHERE End_Date__c = null)
            FROM Engagement__c WHERE Id IN :engagementIds
        ]);
        
        // Get configuration once
        Map<String, Object> configMap = getTaskConfiguration();
        Id taskRecordType = recordTypeCache.get(AppLiterals.RECORD_TYPE_taskHVRX);
        
        List<Case> casesToUpdate = new List<Case>();
        List<EmailUtility.SingleEmailRequestDTO> lstSingleEmailReq = new List<EmailUtility.SingleEmailRequestDTO>();
        Map<Id, User> ownerUserMap = new Map<Id, User>([select id,Name,Email from user]);  
        
        for (Case newCase : newCases) {
            if(!engVsCareTeam.containsKey(newCase.Engagement__c)) continue;
            
            List<String> emailRecipients = new List<String>();
            List<String> emailRecipientsCareTeam = new List<String>();
            Boolean taskCreated = false;
            
            emailRecipients.add(ownerUserMap.get(newCase.ownerId).Email);
            
            Engagement__c engagement = engVsCareTeam.get(newCase.Engagement__c);
            if (engagement != null ){
                if(!engagement.Care_Team_Members__r.isEmpty()) {
                    for (Care_Team_Member__c apcMember : engagement.Care_Team_Members__r) {
                        emailRecipientsCareTeam.add(apcMember.Name__r.Email);
                        if(newCase.Status!= 'Closed'){
                            Task newTask = new Task(
                                WhatId = newCase.Id, 
                                OwnerId = apcMember.Name__c,
                                Subject = (String) configMap.get('subject')+newCase.Review_Type__c,
                                Status = (String) configMap.get('status') ,
                                Priority = (String) configMap.get('priority'),
                                WhoId = engagement.Consumer__c,
                                RecordTypeId = taskRecordType,
                                ActivityDate = System.today().addDays((Integer)configMap.get('dueDate'))
                            );
                            tasksToCreate.add(newTask);
                            taskCreated = true;
                        }
                    }
                }
                else if(!taskCreated && newCase.Status == 'New'){
                     Case caseToUpdate = new Case(Id = newCase.Id, Notes_Comments__c = 'No Care Team Available');
            casesToUpdate.add(caseToUpdate);
                    Task newTask = new Task(
                        WhatId = newCase.Id, 
                        OwnerId = newCase.CreatedById,
                        Subject = (String) configMap.get('subject') + newCase.Review_Type__c,
                        Status = (String) configMap.get('status'),
                        Priority = (String) configMap.get('priority'),
                        WhoId = engagement.Consumer__c,
                        RecordTypeId = taskRecordType,
                        ActivityDate = System.today().addDays((Integer) configMap.get('dueDate'))
                    );
                    tasksToCreate.add(newTask);
                }
            }
        }
        
        // Bulk operations
        if(!tasksToCreate.isEmpty()) {
            try {
                AccessSecurity.insertAsUser(tasksToCreate, null);
            } catch(Exception e) {
                System.debug('Error creating tasks: ' + e.getMessage());
            }
        }
        
        if(!casesToUpdate.isEmpty()) {
            try {
                AccessSecurity.updateAsUser(casesToUpdate, new Set<String>{'Notes_Comments__c'});
            } catch(Exception e) {
                System.debug('Error updating cases: ' + e.getMessage());
            }
        }
    }
    
    // PERFORMANCE OPTIMIZATION: Cache task configuration
    private static Map<String, Object> taskConfigCache;
    
    private static Map<String, Object> getTaskConfiguration() {
        if(taskConfigCache == null) {
            taskConfigCache = new Map<String, Object>();
            // Load from custom metadata or settings
            taskConfigCache.put('subject', 'HVRx Review - ');
            taskConfigCache.put('status', 'Not Started');
            taskConfigCache.put('priority', 'Normal');
            taskConfigCache.put('dueDate', 7);
        }
        return taskConfigCache;
    }
