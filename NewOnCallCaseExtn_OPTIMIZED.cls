public with sharing class NewOnCallCaseExtn {
    public String mode {get;set;}
    public Boolean isNonMember{get;set;}
    public Boolean isEditClicked{get;set;}
    public Boolean isMemberSearch{get;set;}
    public Boolean isCaseMemberSearch{get;set;}
    public boolean isCaseStatusEntInErr{set;get;}
    public Boolean onCallCaseDetail{get;set;}
    public Case caseRecord {get; set;}
    public boolean hasEditPermission {get;set;}
    public boolean hasNewOnCallPermission {get;set;}
    public static String templateData  {get;set;}
    private String engProgStatus;
    private String engMarketName;
    private String engClinicalProgram;

    // PERFORMANCE OPTIMIZATION: Add caching for frequently accessed data
    private static Map<Id, Engagement__c> engagementCache = new Map<Id, Engagement__c>();
    private static Map<Id, Account> accountCache = new Map<Id, Account>();
    private static Map<String, String> timezoneCache = new Map<String, String>();

    public NewOnCallCaseExtn(ApexPages.StandardController controller) {

        isNonMember = false;
        isCaseStatusEntInErr = false;
        isMemberSearch = false;
        isCaseMemberSearch = false;
        onCallCaseDetail = false;
        isEditClicked = false;
        mode = (String)Cache.session.get('onCallCaseMode');
        isNonMember =  ((String)Cache.session.get('isNonMember')) == 'true';
        isMemberSearch =  ((String)Cache.session.get('isMemberSearch')) == 'true';
        isCaseMemberSearch =  (Cache.session.get('isCaseMemberSearch')+'') == 'true';
        isEditClicked =  ((String)Cache.session.get('isEditClicked')) == 'true';
        Object cachedValue = Cache.session.get('onCallCaseDetail');
        if(cachedValue instanceof String){
            onCallCaseDetail =  ((String)Cache.session.get('onCallCaseDetail')) == 'true';
        }else if(cachedValue instanceof Boolean){
            onCallCaseDetail = ((Boolean)Cache.session.get('onCallCaseDetail'));
        }
        System.debug('mode->'+mode);
        Cache.session.remove('isEditClicked');
        Cache.session.remove('onCallCaseMode');
        caseRecord = (Case)controller.getRecord();

        if(isNonMember!= True)
        {
           isNonMember = ((caseRecord.Member_Other_Name__c != Null || caseRecord.ContactId == NULL)? True:False);
        }

	    Cache.session.remove('isNonMember');

 if(isCaseStatusEntInErr != null && isCaseStatusEntInErr!= true)
        {
            isCaseStatusEntInErr = (caseRecord.status ==OnCallAppLiterals.ON_CALL_CASE_PAGE_STATUS_ENTEREDINERROR? true:false);
        }

        hasEditPermission = OnCallUtil.checkCaseEditPermission(caseRecord);
        hasNewOnCallPermission = hasNewOnCallPermission();

        if(String.isNotBlank(caseRecord.Id) && String.isNotBlank(caseRecord.ContactId)){
            templateData=caseDesc_Template_Method(caseRecord.ContactId);
        }

        // PERFORMANCE OPTIMIZATION: Pre-fetch engagement data if needed
        if(caseRecord.Engagement__c !=null){
		   caseRecord.Members_Time_Zone__c=fetchMembersTimeZoneOptimized(caseRecord);
		}
    }

    public boolean hasNewOnCallPermission(){
        return OnCallCaseLwcController.isUserHasPermSet();
    }

	//For Add OnCallNewCase / Add isNonMember case
    // PERFORMANCE OPTIMIZATION: Optimized version with caching
    public String fetchMembersTimeZoneOptimized(Case caseRecord){
        if(caseRecord.Engagement__c == null) return null;

        Engagement__c eng;

        // Check cache first
        if(engagementCache.containsKey(caseRecord.Engagement__c)) {
            eng = engagementCache.get(caseRecord.Engagement__c);
        } else {
            // Query only if not in cache
            List<Engagement__c> engLst = [Select Id,Program_Status_Eng__c,Market__C,Client_Clinical_Program_ID__r.Clinical_Program__c
                                         from Engagement__c where id=: caseRecord.Engagement__c limit 1];
            if(engLst !=null && !engLst.isEmpty() ){
                eng = engLst[0];
                engagementCache.put(eng.Id, eng); // Cache for future use
            }
        }

        if(eng != null) {
            engProgStatus = eng.Program_Status_Eng__c;
            engMarketName = eng.Market__C;
            engClinicalProgram = eng.Client_Clinical_Program_ID__r.Clinical_Program__c;
        }

        if(caseRecord.ContactId != NULL && String.isNotBlank(engMarketName)) {
            System.debug('engMarketName=>'+engMarketName+' , caseRecord.ContactId=>'+caseRecord.ContactId);
            if(mode=='edit' && !isEditClicked) {
                return getTimezoneForMarket(engMarketName);
            }
        }
        return caseRecord.Members_Time_Zone__c;
    }

    // PERFORMANCE OPTIMIZATION: Cached timezone calculation
    private String getTimezoneForMarket(String marketName) {
        if(String.isBlank(marketName)) return null;

        // Check cache first
        if(timezoneCache.containsKey(marketName)) {
            return timezoneCache.get(marketName);
        }

        try {
            string engMarket = marketName.replace(' ','_').replace('-','_');
            On_Call_Market_Vs_Timezone__mdt timezoneMdt = On_Call_Market_Vs_Timezone__mdt.getInstance(engMarket);
            if(timezoneMdt != null && String.isNotBlank(timezoneMdt.Time_Zone__c)) {
                String formattedTime = Datetime.now().format('M/d/yyyy h:mm a z', timezoneMdt.Time_Zone__c);
                timezoneCache.put(marketName, formattedTime); // Cache result
                return formattedTime;
            }
        } catch(Exception e) {
            System.debug('Error calculating timezone: ' + e.getMessage());
        }
        return null;
    }

    public PageReference editModeCancel() {
        return new PageReference('/'+caseRecord.Id).setRedirect(true);
    }
	public PageReference childCaseCancel(){
            return new PageReference('/'+caseRecord.ParentId).setRedirect(true);
    }

    public PageReference Cancel() {
        return new PageReference('/apex/FastOnCallListViews').setRedirect(true);
    }

    public PageReference Save() {
        try{
            AccessSecurity.updateAsUser(caseRecord,null);
            return new PageReference('/'+caseRecord.Id).setRedirect(true);
        }catch(Exception ex){
            ApexPages.addMessage(new ApexPages.Message(ApexPages.Severity.ERROR,ex.getMessage()));
            return null;
        }
    }

    public PageReference Edit() {
        Cache.session.put('onCallCaseMode','edit');
        Cache.session.put('isEditClicked','true');
        return new PageReference('/'+caseRecord.Id).setRedirect(true);
    }

    // PERFORMANCE OPTIMIZATION: Completely optimized createChildCase method
    public PageReference createChildCase() {
        Cache.session.put('onCallCaseMode','edit');

        try {
            // Pre-fetch all required data in bulk queries to minimize database calls
            Map<Id, Engagement__c> engMap = new Map<Id, Engagement__c>();
            Map<Id, Account> accMap = new Map<Id, Account>();

            Set<Id> engIds = new Set<Id>();
            Set<Id> accIds = new Set<Id>();

            // Collect IDs for bulk queries
            if(caseRecord.Engagement__c != null) {
                engIds.add(caseRecord.Engagement__c);
            }
            if(isNonMember && caseRecord.Facility__c != null) {
                accIds.add(caseRecord.Facility__c);
            }

            // Bulk query for engagements (only if not already cached)
            if(!engIds.isEmpty()) {
                for(Id engId : engIds) {
                    if(engagementCache.containsKey(engId)) {
                        engMap.put(engId, engagementCache.get(engId));
                    }
                }

                // Query only missing engagements
                Set<Id> missingEngIds = new Set<Id>();
                for(Id engId : engIds) {
                    if(!engMap.containsKey(engId)) {
                        missingEngIds.add(engId);
                    }
                }

                if(!missingEngIds.isEmpty()) {
                    for(Engagement__c eng : [Select Id,Program_Status_Eng__c,Market__C,Client_Clinical_Program_ID__r.Clinical_Program__c
                                            from Engagement__c where id IN :missingEngIds]) {
                        engMap.put(eng.Id, eng);
                        engagementCache.put(eng.Id, eng); // Update cache
                    }
                }
            }

            // Bulk query for accounts (only if not already cached)
            if(!accIds.isEmpty()) {
                for(Id accId : accIds) {
                    if(accountCache.containsKey(accId)) {
                        accMap.put(accId, accountCache.get(accId));
                    }
                }

                // Query only missing accounts
                Set<Id> missingAccIds = new Set<Id>();
                for(Id accId : accIds) {
                    if(!accMap.containsKey(accId)) {
                        missingAccIds.add(accId);
                    }
                }

                if(!missingAccIds.isEmpty()) {
                    for(Account acc : [Select id,Market__c from Account where id IN :missingAccIds]) {
                        accMap.put(acc.Id, acc);
                        accountCache.put(acc.Id, acc); // Update cache
                    }
                }
            }

            // Create child case with optimized data access
            Case childCase = new Case();
            childCase = caseRecord.clone(false, true, false, false);
            childCase.ParentId = caseRecord.Id;
            childCase.OwnerId = UserInfo.getUserId();
            childCase.Subject ='On Call Support';
            childCase.Status = 'Entered In Error';
            childCase.Admission__c = 'Other';
            childCase.Person_Calling__c = '';
            childCase.Inbound_or_Outbound__c = 'Outbound';
            childCase.description='';
            childCase.Date_and_Time__c=System.now();

            // Use cached/bulk-queried data instead of individual queries
            if(childCase.Engagement__c !=null && engMap.containsKey(childCase.Engagement__c)){
                mode='edit';
                isEditClicked=false;

                Engagement__c eng = engMap.get(childCase.Engagement__c);
                childCase.Market__c = eng.Market__C;
                childCase.Clinical_Program__c = eng.Client_Clinical_Program_ID__r.Clinical_Program__c;
                childCase.Members_Time_Zone__c = getTimezoneForMarket(eng.Market__C);
            }

            if(isNonMember && childCase.Facility__c != null && accMap.containsKey(childCase.Facility__c)) {
                childCase.Market__c = accMap.get(childCase.Facility__c).Market__c;
            }

            // Single DML operation
            AccessSecurity.insertAsUSer(childCase,null);
            return new PageReference('/'+childCase.id).setRedirect(true);

        } catch(Exception ex) {
            ApexPages.addMessage(new ApexPages.Message(ApexPages.Severity.ERROR, 'Error creating child case: ' + ex.getMessage()));
            System.debug('Error in createChildCase: ' + ex.getMessage());
            return null;
        }
    }

    // PERFORMANCE OPTIMIZATION: Clear caches when needed (optional method for cache management)
    public static void clearCaches() {
        engagementCache.clear();
        accountCache.clear();
        timezoneCache.clear();
    }

    public PageReference cancelNonMemberCase() {
        return new PageReference('/apex/FastOnCallListViews?Id='+caseRecord.Id);
    }

    public String caseDesc_Template_Method(String contactId){

        List<Contact> contactList = [select id,name,Birthdate,Gender__c,Primary_Phone__c,OtherStreet,OtherCity,OtherState,OtherPostalCode from Contact where id =: contactId];

        if(contactList != null && contactList.size()>0){
            Contact con = contactList[0];
            templateData = 'Member Name: '+con.name;
            if(con.Birthdate != null){
                templateData += '\nDOB: '+con.Birthdate.format();
            }
            if(con.Gender__c != null){
                templateData += '\nGender: '+con.Gender__c;
            }
            if(con.Primary_Phone__c != null){
                templateData += '\nPhone: '+con.Primary_Phone__c;
            }
            if(con.OtherStreet != null || con.OtherCity != null || con.OtherState != null || con.OtherPostalCode != null){
                templateData += '\nAddress: ';
                if(con.OtherStreet != null){
                    templateData += con.OtherStreet+' ';
                }
                if(con.OtherCity != null){
                    templateData += con.OtherCity+' ';
                }
                if(con.OtherState != null){
                    templateData += con.OtherState+' ';
                }
                if(con.OtherPostalCode != null){
                    templateData += con.OtherPostalCode;
                }
            }
        }

        return templateData;
    }

    private String fetchHub(String market) {
        String marketApi;
        Map<String, On_Call_Market_Vs_Hub__mdt> marketHubMap = On_Call_Market_Vs_Hub__mdt.getAll();
        if(String.isNotBlank(market) && market.contains('-')){
            marketApi = market.replaceAll('-', '_');
        }else{
        	marketApi = market.replaceAll(' ', '_');
        }
        String hub = marketHubMap.get(marketApi)!=null?marketHubMap.get(marketApi).Hub__c:'';
        return hub;
    }

    public List<SelectOption> getStatusValues (){
        List<SelectOption> options = new List<SelectOption>();
        if(mode.equalsIgnoreCase('edit') && !isEditClicked){
            options.add(new SelectOption(OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_NEW, OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_NEW));
            options.add(new SelectOption(OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_PROG, OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_PROG));
            options.add(new SelectOption(OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_COMPLETED, OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_COMPLETED));
        }else if(mode.equalsIgnoreCase('edit') && isEditClicked){
            options.add(new SelectOption(OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_NEW, OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_NEW));
            options.add(new SelectOption(OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_PROG, OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_PROG));
            options.add(new SelectOption(OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_COMPLETED, OnCallAppLiterals.ON_CALL_CASE_PAGE_SELECT_LIST_OPTIONS_COMPLETED));
            options.add(new SelectOption(OnCallAppLiterals.ON_CALL_CASE_PAGE_STATUS_ENTEREDINERROR, OnCallAppLiterals.ON_CALL_CASE_PAGE_STATUS_ENTEREDINERROR));
        }
        return options;
    }
}
