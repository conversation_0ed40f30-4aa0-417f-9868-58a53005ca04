/*
 Uses : This class is used in 60 days batch to update CCFs records when risk level changed.
*/

global with sharing class Batch_SendEmail_RiskLevel_Update implements Database.Batchable<sObject>, Schedulable, Database.Stateful {
    String DefaultHospiceRiskLevel='';
	global Database.QueryLocator start (Database.BatchableContext BC){
        List<String> ClinicalPrograms=new List<String>(); 
        if(Risk_Level_Engagement_Clinical_Programs__mdt.getInstance('Clinical_Programs') != NULL){
           ClinicalPrograms = Risk_Level_Engagement_Clinical_Programs__mdt.getInstance('Clinical_Programs').value__c.split(',');
		}        
        if(Risk_Level_Engagement_Clinical_Programs__mdt.getInstance('Default_Hospice_Risk_Level') != NULL){
           DefaultHospiceRiskLevel = Risk_Level_Engagement_Clinical_Programs__mdt.getInstance('Default_Hospice_Risk_Level').value__c;
		}               
		return Database.getQueryLocator([SELECT Id, Risk_Level__c, Risk_Date__c, (SELECT Id, Name, RecordType.Name, Risk_Level__c, Engagement_ID__c, Engagement_ID__r.Program_Status_Eng__c, Engagement_ID__r.Clinical_Program__c, Engagement_ID__r.Risk_Level__c, Data_Source_ID__c, Data_Source_ID__r.Name, CreatedDate, LastModifiedDate, Last_Reviewed_Date__c, Engagement_ID__r.consumer__c, Risk_Assessment_Name__c FROM Consumer_Clinical_Findings__r 
										 WHERE Risk_Level__c !=null AND RecordType.Name=:AppLiterals.CONSUMER_RISK_ASSESSMENT AND((Data_Source_ID__r.Name=:AppLiterals.CLINICAL and Last_Reviewed_Date__c >=:System.today()-Integer.ValueOf(System.Label.Risk_Level_Date_Range)) OR (Data_Source_ID__r.Name=:AppLiterals.HCE and Last_Reviewed_Date__c >=:System.today()-Integer.ValueOf(System.Label.Risk_Level_Date_Range))) Order by Last_Reviewed_Date__c DESC)
										 from Engagement__c WHERE Program_Status_Eng__c=:AppLiterals.STATUS_OPEN AND Clinical_Program__c IN : ClinicalPrograms]);
    }                              	

    List<Engagement__c> EngToUpdateList = new List<Engagement__c>();
    Map<Id, string> mapEngIdToOldRiskLevel=new Map<Id, string>();
    Map<Id, Id> mapEngIdToNewRiskLevelId=new Map<Id, Id>();
    

	global void execute(Database.BatchableContext BC, List<Engagement__c> EngRecords){
		try{
			List<Consumer_Clinical_Finding__c> HCE_CCFRecords = new List<Consumer_Clinical_FInding__c>();
            List<Consumer_Clinical_Finding__c> Clinical_CCFRecords = new List<Consumer_Clinical_FInding__c>();
			Map<Id, List<Consumer_Clinical_Finding__c>> AllEngCCFMap = new Map<Id, List<Consumer_Clinical_Finding__c>>();      
			Map<Integer,String> RiskLevelMap = new Map<Integer,String>{1=>'Green',2=>'Yellow',3=>'Red'};
                
			// Building Map for engagement with their CCFs records			
			for(Engagement__c eng : EngRecords){               
                if(eng.Consumer_Clinical_Findings__r.size()>0) {
                    AllEngCCFMap.put(eng.Id, eng.Consumer_Clinical_Findings__r);
                }                                     
			}

			//Find Active Hospice Supplimental service and Remove Engagement which have active Hospice type Supplimental service. If Risk level is Green (DefaultHospiceRiskLevel) and the priority CCF record is HCE type then only update and remove from the IESNPEngagementAndCCFMap.
			for(Eng_Prog_Supp_Services__c ss : [SELECT Id, Supplemental_Service__c, Engagement__c, Engagement__r.Program_Status_Eng__c, Start_Date__c, End_Date__c, CreatedDate, Engagement__r.Risk_Level__c, Engagement__r.Risk_Date__c FROM Eng_Prog_Supp_Services__c WHERE Engagement__c IN : AllEngCCFMap.keySet() and Supplemental_Service__c=:AppLiterals.HOSPICE AND Start_Date__c <= TODAY AND (End_Date__c = null OR End_Date__c >= TODAY) order by CreatedDate DESC]){  
			if(AllEngCCFMap.containsKey(ss.Engagement__c)){
				System.debug('updateIESNPEngRecordRiskLevel ss=>'+ss+', Eng Risk=>'+ss.Engagement__r.Risk_Level__c+' , AllEngCCFMap=>'+AllEngCCFMap);                  
				if(ss.Engagement__r.Risk_Level__c!=DefaultHospiceRiskLevel){                 
				   EngToUpdateList.add(new Engagement__c(id=ss.Engagement__c, Risk_Level__c=DefaultHospiceRiskLevel, Risk_Date__c=ss.Start_Date__c));					  
				}                    
			}
			AllEngCCFMap.remove(ss.Engagement__c);
			} 
            
          
								
			// Cheking and updating risk for each engagement
			for(ID engId : AllEngCCFMap.keySet()){
				HCE_CCFRecords = new List<Consumer_Clinical_FInding__c>(); 
                Clinical_CCFRecords = new List<Consumer_Clinical_FInding__c>();				
				List<Consumer_Clinical_FInding__c> CCFList = AllEngCCFMap.get(engId);              
  
				for(Integer i =0; i<CCFList.size(); i++){                   
					if (CCFList[i].Data_Source_ID__r.Name == AppLiterals.CLINICAL) {
                       Clinical_CCFRecords.add(CCFList[i]);
					} else if (CCFList[i].Data_Source_ID__r.Name == AppLiterals.HCE) { 
                        HCE_CCFRecords.add(CCFList[i]);						
                    }                    
				}
                
                //Checking and comparing the 90th day clinical with latest HCE CCF record.
                if(Clinical_CCFRecords.size()>0 && HCE_CCFRecords.size()>0 ){                   
                   if(Clinical_CCFRecords[0].Last_Reviewed_Date__c == System.today()-Integer.ValueOf(System.Label.Risk_Level_Date_Range) && HCE_CCFRecords[0].Risk_Level__c != Clinical_CCFRecords[0].Risk_Level__c){ //  && HCE_CCFRecords[0].Last_Reviewed_Date__c > clinicalCCFRecord.Last_Reviewed_Date__c
					Consumer_Clinical_Finding__c selectedCCFRecord = HCE_CCFRecords[0];					
                    System.debug('CCFRiskUpdateBatch execute Eng old Risk Level==>'+selectedCCFRecord.Engagement_ID__r.Risk_Level__c+' ,selectedCCFRecord =>'+selectedCCFRecord);                   
					if(selectedCCFRecord.Engagement_ID__r.Risk_Level__c != RiskLevelMap.get(Integer.valueOf(selectedCCFRecord.Risk_Level__c))){
                        mapEngIdToOldRiskLevel.put(selectedCCFRecord.Engagement_ID__c,selectedCCFRecord.Engagement_ID__r.Risk_Level__c);
					    mapEngIdToNewRiskLevelId.put(selectedCCFRecord.Engagement_ID__c,selectedCCFRecord.id);   
                        Engagement__c eng = new Engagement__c(Id=selectedCCFRecord.Engagement_ID__c); 
						eng.Risk_Level__c = RiskLevelMap.get(Integer.valueOf(selectedCCFRecord.Risk_Level__c));
						eng.Risk_Date__c = selectedCCFRecord.Last_Reviewed_Date__c;  
						EngToUpdateList.add(eng); 						                    
					} 
				   }
                }                              
			} 
			System.debug('EngToUpdateListSize==>'+EngToUpdateList.size()+' ,EngToUpdateList=>'+EngToUpdateList); 
		}Catch(Exception ex){
			System.debug('Exception=>'+ex.getMessage());
		}
	}
	   
   	global void finish (Database.BatchableContext BC){
         if(EngToUpdateList.size()>0){
            SendEmailToAPC(EngToUpdateList,mapEngIdToNewRiskLevelId,mapEngIdToOldRiskLevel);
            
        }
	}
            
     private  static void SendEmailToAPC(List<Engagement__c> engRecords,Map<Id, Id> mapEngIdToNewRiskLevelId,Map<Id, string> mapEngIdToOldRiskLevel)
    	{
         System.debug('engRecords' + engRecords);  
        Set<Id> engId=new Set<Id>();
        Map<Id,List<Care_Team_Member__c>> mapEngCTMList=new Map<Id,List<Care_Team_Member__c>>(); 
        Map<Id, ID> mapEngToPVPId=new Map<Id, ID>();    
        Map<Id, List<Id>> mapEngToPVPRNIds=new Map<Id, List<Id>>();    
        List<RiskLevelDataWrapper> RiskLevelDataList=new List<RiskLevelDataWrapper>(); 
        Map<Id,List<RiskLevelDataWrapper>> ccfUserMap=new Map<Id,List<RiskLevelDataWrapper>>();
        for(Engagement__c engagement : engRecords)
        {
            engId.add(engagement.id); 
        }

           List<Care_Team_Member__c> careTeamList=[select id,Name__c,Engagement__c,Engagement__r.Clinical_Program__c,Role__c,End_Date__c,name__r.id,name__r.name from Care_Team_Member__c where Engagement__c in : engId and role__c in ('Primary Visiting Provider','CSM','Registered Nurse (RN)') and (End_Date__c=null OR End_Date__c>TODAY)];
          for(Care_Team_Member__c careTeam : careTeamList)
            {
                if(careTeam.role__c=='Primary Visiting Provider' && careTeam.Engagement__r.Clinical_Program__c == 'IESNP')
                {
                    mapEngToPVPId.put(careTeam.Engagement__c,careTeam.Name__c); 
                }else if((careTeam.role__c=='Primary Visiting Provider' || careTeam.role__c=='Registered Nurse (RN)') && careTeam.Engagement__r.Clinical_Program__c == 'ISNP')
                {
                    if(mapEngToPVPRNIds.containsKey(careTeam.Engagement__c)){
                        mapEngToPVPRNIds.get(careTeam.Engagement__c).add(careTeam.Name__c);
                    }else{
                        mapEngToPVPRNIds.put(careTeam.Engagement__c,new List<Id>{careTeam.Name__c});
                    }
                }
            if(mapEngCTMList.containsKey(careTeam.Engagement__c)){  
                mapEngCTMList.get(careTeam.Engagement__c).add(careTeam); 
            }else{
                mapEngCTMList.put(careTeam.Engagement__c,new List<Care_Team_Member__c>{careTeam});
            }            
            }		  
        Map<Id, Engagement__c> engMap=new Map<Id, Engagement__c>([select id,Consumer__c,Consumer__r.name from Engagement__c where id in : engId ]);
        for(Engagement__c eng : engRecords)
        {
           string oldRiskLevel=mapEngIdToOldRiskLevel.get(eng.id);
           RiskLevelDataWrapper riskLevelRecords=new RiskLevelDataWrapper(); 
           riskLevelRecords.memberName=engMap.get(eng.id).Consumer__r.name;
           riskLevelRecords.oldRiskLevel=oldRiskLevel==null ? '' : oldRiskLevel;
           riskLevelRecords.newRiskLevel=eng.Risk_Level__c;
           riskLevelRecords.RiskLevelId=mapEngIdToNewRiskLevelId.get(eng.id);
           riskLevelRecords.engId=eng.id;
           riskLevelRecords.memberId=engMap.get(eng.id).Consumer__c; 
           Id pvpId=mapEngToPVPId.get(eng.id);
            List<Id> pvprnIds = new List<Id>();
            If(mapEngToPVPRNIds.size() > 0) {
                pvprnIds = mapEngToPVPRNIds.get(eng.id);
            }
            if(pvpId!=null)
            {
               if(ccfUserMap.containsKey(pvpId)){
                ccfUserMap.get(pvpId).add(riskLevelRecords);
            }else{
                ccfUserMap.put(pvpId,new List<RiskLevelDataWrapper>{riskLevelRecords});
            }  
			}
            if(pvprnIds != null) {
                for(Id pvprnId: pvprnIds) {
                    if(ccfUserMap.containsKey(pvprnId)){
                        ccfUserMap.get(pvprnId).add(riskLevelRecords);
                    }else{
                        ccfUserMap.put(pvprnId,new List<RiskLevelDataWrapper>{riskLevelRecords});
                    }
                }
            }
        }
        System.debug('ccfUserMap' + ccfUserMap); 
        prepareBodyAndSendEmail(ccfUserMap,mapEngCTMList); 
       
    }
    
     private static void prepareBodyAndSendEmail(Map<Id,List<RiskLevelDataWrapper>> ccfUserMap,Map<Id,List<Care_Team_Member__c>> mapEngCTMList){
        Integer intMonth = Date.Today().Month(); 
        Integer currentYear = Date.Today().year();
        Integer chunkCounter=1;
        String subject = 'Reminder: Clinically Set Risk Stratification about to Expire';
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage(); 
        Set<Id>usrIds=ccfUserMap.keySet();
        Map<Id,User> usrMap = new Map<Id,User>([SELECT Id, name,Email FROM User Where id in : usrIds]);
        List<EmailUtility.SingleEmailRequestDTOWithCC> lstSingleEmailReq = new List<EmailUtility.SingleEmailRequestDTOWithCC>();
        List<List<EmailUtility.SingleEmailRequestDTOWithCC>> chunkSetEmail=new List<List<EmailUtility.SingleEmailRequestDTOWithCC>>();
        String sfdcBaseURL = Url.getOrgDomainUrl().toExternalForm(); 
          for(String key : ccfUserMap.keySet()){
            String apcName = (usrMap.get(key)!=null ? usrMap.get(key).Name : '');
            String htmlBody = '<div> Hi '+ apcName +',</div><br><br><p>Please see your list of members who have a risk level that was set per clinical judgement.</p><font color="#ff0000">Action: If you agree with HCE risk level, no further action is needed.</br></br>If you disagree with HCE risk level as member needs a higher level of care, then you need to update the risk level by using the hyperlink below.</br></br></font>';
             htmlBody+='<table border="1" style="border-collapse: collapse"><tr style="background-color:#d2e9fa"><th>Member Name</th><th>Current Risk Level</th><th>Automated Risk Level</th><th>Date Clinical Risk Level will Expire</th><th>Risk Record</th></tr>';            
              List<string> rnCSMuserIdList=new List<string>();
              for(RiskLevelDataWrapper ccf : ccfUserMap.get(key)){ 
                String PatientName = ccf.memberName;
                String PreviousRiskLevel =ccf.oldRiskLevel;
                String currentRiskLevel =ccf.newRiskLevel; 
                string clinicalRiskLevelDate=string.valueof(System.today()+30);
               // String hyperLinkToPatientRecord = sfdcBaseURL + '/apex/RiskLevelView?aeid='+ccf.engID + '&acid=' +ccf.memberId + '&recId='+ccf.RiskLevelId;
               String hyperLinkToPatientRecord = sfdcBaseURL + '/apex/DetailEngagement_LD?id='+ccf.engID + '&navPage=RiskLevel';
                htmlBody += '<tr><td>' + PatientName + '</td><td><center>' + PreviousRiskLevel + '</center></td><td><center>' + currentRiskLevel + '</center></td><td><center>'+ clinicalRiskLevelDate + '</center></td><td>' + '<a href='+hyperLinkToPatientRecord+'>'+'Go To Risk Level </a>'  + '</td></tr>';
                List<Care_Team_Member__c> careTeamList= mapEngCTMList.get(ccf.engID); 
                  if(careTeamList!=null && careTeamList.size()>0)
                  {
                    for(Care_Team_Member__c ccm : careTeamList)
                  {
                    if((ccm.Name__c!=null) && (ccm.role__c=='Registered Nurse (RN)' || ccm.role__c=='CSM'))
                    {
                      rnCSMuserIdList.add(ccm.Name__c);  
                    }  
                  } 
                  }                                 
              }
             
            htmlBody += '</table>';
			htmlBody += '<br><b>For any queries or concerns, please open a Service Now ticket for the Pathway support team, specifically for the Pathway SCC App-SPT.</b><br/>';
       
            htmlBody += '<br><br>Sincerely,<br>Pathway Team';
               
           List<string> emailIdList = new List<String>{key}; 
            EmailUtility.SingleEmailRequestDTOWithCC eUtility =
                new EmailUtility.SingleEmailRequestDTOWithCC(emailIdList, htmlBody, subject, null , null, null,rnCSMuserIdList);            
            lstSingleEmailReq.add(eUtility);
            if(chunkCounter==100){
                chunkSetEmail.add(lstSingleEmailReq);
                chunkCounter=0;
                lstSingleEmailReq = new List<EmailUtility.SingleEmailRequestDTOWithCC>();
            }            
            chunkCounter=chunkCounter+1;
            
        }
        
        
        if(lstSingleEmailReq.size()>0){
            chunkSetEmail.add(lstSingleEmailReq);
        }
          System.debug('chunkSetEmail' + chunkSetEmail);                            
        for(List<EmailUtility.SingleEmailRequestDTOWithCC> lstSinglEmail:chunkSetEmail){
          	if(lstSinglEmail.size()>0)
            {   
                EmailUtility.EmailWrapperDTOWithCC emailWrapper = new EmailUtility.EmailWrapperDTOWithCC();
                emailWrapper.lstEmailReq = lstSinglEmail;
                emailWrapper.sendAllOrNone = true; 
                EmailUtility.sendEmailWithCC(emailWrapper);
            }  
        }

          }
           

	public void execute(SchedulableContext sc){  
		 Batch_SendEmail_RiskLevel_Update batchJob=new Batch_SendEmail_RiskLevel_Update();
		 Database.executeBatch(batchJob,Integer.valueOf(CCF_Risk_Update_Batch_Size__mdt.getInstance('Batch_Size').value__c));
	}
    
     public class RiskLevelDataWrapper 
    {

        public Id engID {get;set;}
        public Id memberId {get;set;}
        public string memberName {get;set;}
        public string oldRiskLevel {get;set;}
        public string newRiskLevel {get;set;}
        public string RiskLevelId {get;set;}
        public string clinicalRiskLevelDate {get;set;}
    }

	}